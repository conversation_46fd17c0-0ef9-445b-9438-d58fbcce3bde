<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * WooCommerce Inventory Manager
 *
 * Manages advanced inventory features including real-time tracking, low stock alerts,
 * supplier management, and inventory forecasting
 */
class DAB_Inventory_Manager {

    /**
     * Initialize the Inventory Manager
     */
    public static function init() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Hook into WooCommerce inventory actions
        add_action('woocommerce_product_set_stock', array(__CLASS__, 'track_stock_change'), 10, 1);
        add_action('woocommerce_variation_set_stock', array(__CLASS__, 'track_stock_change'), 10, 1);

        // Low stock alerts
        add_action('woocommerce_low_stock', array(__CLASS__, 'handle_low_stock_alert'));
        add_action('woocommerce_no_stock', array(__CLASS__, 'handle_out_of_stock_alert'));

        // Custom inventory fields
        add_action('woocommerce_product_options_inventory_product_data', array(__CLASS__, 'add_inventory_fields'));
        add_action('woocommerce_process_product_meta', array(__CLASS__, 'save_inventory_fields'));

        // Supplier management
        add_action('add_meta_boxes', array(__CLASS__, 'add_supplier_meta_box'));
        add_action('save_post', array(__CLASS__, 'save_supplier_data'));

        // Inventory reports
        add_action('admin_menu', array(__CLASS__, 'add_inventory_reports_menu'));

        // AJAX handlers
        add_action('wp_ajax_dab_get_inventory_data', array(__CLASS__, 'ajax_get_inventory_data'));
        add_action('wp_ajax_dab_update_stock_levels', array(__CLASS__, 'ajax_update_stock_levels'));
        add_action('wp_ajax_dab_create_purchase_order', array(__CLASS__, 'ajax_create_purchase_order'));

        // Scheduled tasks
        add_action('dab_inventory_daily_check', array(__CLASS__, 'daily_inventory_check'));
        if (!wp_next_scheduled('dab_inventory_daily_check')) {
            wp_schedule_event(time(), 'daily', 'dab_inventory_daily_check');
        }
    }

    /**
     * Create database tables for inventory management
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Inventory tracking table
        $inventory_table = $wpdb->prefix . 'dab_wc_inventory_tracking';

        $sql_inventory = "CREATE TABLE $inventory_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            variation_id bigint(20) DEFAULT 0,
            stock_change int(11) NOT NULL,
            stock_level_before int(11) NOT NULL,
            stock_level_after int(11) NOT NULL,
            change_reason varchar(100) DEFAULT 'manual',
            order_id bigint(20) DEFAULT 0,
            user_id bigint(20) DEFAULT 0,
            notes text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY variation_id (variation_id),
            KEY order_id (order_id),
            KEY created_at (created_at)
        ) $charset_collate;";

        // Suppliers table
        $suppliers_table = $wpdb->prefix . 'dab_wc_suppliers';

        $sql_suppliers = "CREATE TABLE $suppliers_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            supplier_name varchar(255) NOT NULL,
            supplier_email varchar(255),
            supplier_phone varchar(50),
            supplier_address text,
            supplier_website varchar(255),
            contact_person varchar(255),
            payment_terms varchar(100),
            lead_time_days int(11) DEFAULT 7,
            minimum_order_value decimal(10,2) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            notes text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY supplier_name (supplier_name)
        ) $charset_collate;";

        // Product suppliers relationship table
        $product_suppliers_table = $wpdb->prefix . 'dab_wc_product_suppliers';

        $sql_product_suppliers = "CREATE TABLE $product_suppliers_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            supplier_id mediumint(9) NOT NULL,
            supplier_sku varchar(100),
            cost_price decimal(10,2),
            minimum_quantity int(11) DEFAULT 1,
            lead_time_days int(11) DEFAULT 7,
            is_primary tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY product_supplier (product_id, supplier_id),
            KEY product_id (product_id),
            KEY supplier_id (supplier_id)
        ) $charset_collate;";

        // Purchase orders table
        $purchase_orders_table = $wpdb->prefix . 'dab_wc_purchase_orders';

        $sql_purchase_orders = "CREATE TABLE $purchase_orders_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            po_number varchar(50) NOT NULL,
            supplier_id mediumint(9) NOT NULL,
            status varchar(20) DEFAULT 'pending',
            order_date date NOT NULL,
            expected_delivery_date date,
            actual_delivery_date date,
            total_amount decimal(10,2) DEFAULT 0,
            notes text,
            created_by bigint(20),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY po_number (po_number),
            KEY supplier_id (supplier_id),
            KEY status (status),
            KEY order_date (order_date)
        ) $charset_collate;";

        // Purchase order items table
        $po_items_table = $wpdb->prefix . 'dab_wc_purchase_order_items';

        $sql_po_items = "CREATE TABLE $po_items_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            purchase_order_id mediumint(9) NOT NULL,
            product_id bigint(20) NOT NULL,
            quantity_ordered int(11) NOT NULL,
            quantity_received int(11) DEFAULT 0,
            unit_cost decimal(10,2) NOT NULL,
            total_cost decimal(10,2) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY purchase_order_id (purchase_order_id),
            KEY product_id (product_id)
        ) $charset_collate;";

        // Low stock alerts table
        $alerts_table = $wpdb->prefix . 'dab_wc_stock_alerts';

        $sql_alerts = "CREATE TABLE $alerts_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            variation_id bigint(20) DEFAULT 0,
            alert_type varchar(20) NOT NULL,
            current_stock int(11) NOT NULL,
            threshold_level int(11) NOT NULL,
            alert_sent tinyint(1) DEFAULT 0,
            alert_sent_at datetime,
            resolved tinyint(1) DEFAULT 0,
            resolved_at datetime,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY variation_id (variation_id),
            KEY alert_type (alert_type),
            KEY resolved (resolved)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_inventory);
        dbDelta($sql_suppliers);
        dbDelta($sql_product_suppliers);
        dbDelta($sql_purchase_orders);
        dbDelta($sql_po_items);
        dbDelta($sql_alerts);

        // Create default suppliers
        self::create_default_suppliers();
    }

    /**
     * Create default suppliers
     */
    public static function create_default_suppliers() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_suppliers';

        $default_suppliers = array(
            array(
                'supplier_name' => 'Default Supplier',
                'supplier_email' => '<EMAIL>',
                'contact_person' => 'Supplier Manager',
                'payment_terms' => 'Net 30',
                'lead_time_days' => 7,
                'minimum_order_value' => 100.00
            )
        );

        foreach ($default_suppliers as $supplier) {
            $existing = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM $table_name WHERE supplier_name = %s",
                    $supplier['supplier_name']
                )
            );

            if (!$existing) {
                $wpdb->insert($table_name, $supplier);
            }
        }
    }

    /**
     * Track stock changes
     */
    public static function track_stock_change($product) {
        global $wpdb;

        $product_id = $product->get_id();
        $parent_id = $product->get_parent_id();
        $variation_id = $parent_id ? $product_id : 0;
        $actual_product_id = $parent_id ? $parent_id : $product_id;

        $current_stock = $product->get_stock_quantity();
        $previous_stock = get_post_meta($product_id, '_previous_stock', true);

        if ($previous_stock === '') {
            $previous_stock = $current_stock;
        }

        $stock_change = $current_stock - $previous_stock;

        if ($stock_change != 0) {
            $table_name = $wpdb->prefix . 'dab_wc_inventory_tracking';

            $wpdb->insert(
                $table_name,
                array(
                    'product_id' => $actual_product_id,
                    'variation_id' => $variation_id,
                    'stock_change' => $stock_change,
                    'stock_level_before' => $previous_stock,
                    'stock_level_after' => $current_stock,
                    'change_reason' => self::determine_change_reason(),
                    'user_id' => get_current_user_id()
                ),
                array('%d', '%d', '%d', '%d', '%d', '%s', '%d')
            );
        }

        // Update previous stock for next comparison
        update_post_meta($product_id, '_previous_stock', $current_stock);

        // Check for low stock alerts
        self::check_low_stock_alert($product);
    }

    /**
     * Determine the reason for stock change
     */
    public static function determine_change_reason() {
        // Check if we're in an order context
        if (did_action('woocommerce_checkout_order_processed')) {
            return 'order_sale';
        }

        // Check if we're in admin context
        if (is_admin()) {
            return 'manual_adjustment';
        }

        // Check if we're processing a refund
        if (did_action('woocommerce_order_refunded')) {
            return 'refund';
        }

        return 'unknown';
    }

    /**
     * Check for low stock alerts
     */
    public static function check_low_stock_alert($product) {
        $product_id = $product->get_id();
        $current_stock = $product->get_stock_quantity();
        $low_stock_threshold = $product->get_low_stock_amount();

        if ($low_stock_threshold && $current_stock <= $low_stock_threshold) {
            self::create_stock_alert($product_id, 'low_stock', $current_stock, $low_stock_threshold);
        }

        if ($current_stock <= 0) {
            self::create_stock_alert($product_id, 'out_of_stock', $current_stock, 0);
        }
    }

    /**
     * Create stock alert
     */
    public static function create_stock_alert($product_id, $alert_type, $current_stock, $threshold) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'dab_wc_stock_alerts';

        // Check if alert already exists and is not resolved
        $existing_alert = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM $table_name WHERE product_id = %d AND alert_type = %s AND resolved = 0",
                $product_id,
                $alert_type
            )
        );

        if (!$existing_alert) {
            $wpdb->insert(
                $table_name,
                array(
                    'product_id' => $product_id,
                    'alert_type' => $alert_type,
                    'current_stock' => $current_stock,
                    'threshold_level' => $threshold
                ),
                array('%d', '%s', '%d', '%d')
            );

            // Send alert notification
            self::send_stock_alert_notification($product_id, $alert_type, $current_stock);
        }
    }

    /**
     * Send stock alert notification
     */
    public static function send_stock_alert_notification($product_id, $alert_type, $current_stock) {
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        $product_name = $product->get_name();
        $admin_email = get_option('admin_email');

        $subject = sprintf(
            __('[%s] %s Alert: %s', 'db-app-builder'),
            get_bloginfo('name'),
            ucfirst(str_replace('_', ' ', $alert_type)),
            $product_name
        );

        $message = sprintf(
            __('Product: %s (ID: %d)%sAlert Type: %s%sCurrent Stock: %d%s%sPlease review and take appropriate action.', 'db-app-builder'),
            $product_name,
            $product_id,
            "\n",
            ucfirst(str_replace('_', ' ', $alert_type)),
            "\n",
            $current_stock,
            "\n\n",
            "\n"
        );

        wp_mail($admin_email, $subject, $message);
    }

    /**
     * Add inventory reports menu
     */
    public static function add_inventory_reports_menu() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        add_submenu_page(
            'woocommerce',
            __('Inventory Reports', 'db-app-builder'),
            __('Inventory Reports', 'db-app-builder'),
            'manage_woocommerce',
            'dab-inventory-reports',
            array(__CLASS__, 'render_inventory_reports_page')
        );
    }

    /**
     * Render inventory reports page
     */
    public static function render_inventory_reports_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Inventory Reports', 'db-app-builder'); ?></h1>

            <div class="dab-inventory-reports">
                <div class="dab-reports-grid">
                    <div class="dab-report-card">
                        <h3><?php _e('Low Stock Alert', 'db-app-builder'); ?></h3>
                        <div id="low-stock-report"></div>
                    </div>

                    <div class="dab-report-card">
                        <h3><?php _e('Stock Movement', 'db-app-builder'); ?></h3>
                        <div id="stock-movement-report"></div>
                    </div>

                    <div class="dab-report-card">
                        <h3><?php _e('Supplier Performance', 'db-app-builder'); ?></h3>
                        <div id="supplier-performance-report"></div>
                    </div>
                </div>
            </div>

            <script>
            jQuery(document).ready(function($) {
                // Load inventory reports data
                loadInventoryReports();
            });

            function loadInventoryReports() {
                // Implementation for loading reports
                console.log('Loading inventory reports...');
            }
            </script>

            <style>
            .dab-reports-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }

            .dab-report-card {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .dab-report-card h3 {
                margin-top: 0;
                color: #333;
            }
            </style>
        </div>
        <?php
    }
}
