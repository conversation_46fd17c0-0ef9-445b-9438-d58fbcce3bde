/**
 * Database App Builder - Templates JavaScript
 * 
 * Handles template browsing, preview, and installation
 */

jQuery(document).ready(function($) {
    'use strict';

    let currentTemplateId = null;

    // Template search functionality
    $('#search-templates').on('click', function() {
        searchTemplates();
    });

    $('#template-search').on('keypress', function(e) {
        if (e.which === 13) {
            searchTemplates();
        }
    });

    $('#template-category').on('change', function() {
        searchTemplates();
    });

    // Template preview
    $(document).on('click', '.dab-preview-template', function() {
        const templateId = $(this).data('template-id');
        previewTemplate(templateId);
    });

    // Template installation
    $(document).on('click', '.dab-install-template', function() {
        const templateId = $(this).data('template-id');
        const templateName = $(this).data('template-name');
        showInstallModal(templateId, templateName);
    });

    // Install from preview
    $('#dab-install-from-preview').on('click', function() {
        if (currentTemplateId) {
            $('#dab-template-preview-modal').hide();
            const templateName = $('#dab-preview-title').text().replace('Template Preview - ', '');
            showInstallModal(currentTemplateId, templateName);
        }
    });

    // Confirm installation
    $('#dab-confirm-install').on('click', function() {
        const installationName = $('#installation-name').val().trim();
        
        if (!installationName) {
            alert(dab_templates.messages.installation_name_required || 'Please enter an installation name.');
            return;
        }

        if (!currentTemplateId) {
            alert('No template selected.');
            return;
        }

        installTemplate(currentTemplateId, installationName);
    });

    // Delete installation
    $(document).on('click', '.dab-delete-installation', function() {
        const installationId = $(this).data('installation-id');
        const installationName = $(this).data('installation-name');
        
        if (confirm(dab_templates.messages.confirm_delete.replace('%s', installationName))) {
            deleteInstallation(installationId);
        }
    });

    // Modal controls
    $('.dab-modal-close, .dab-modal-overlay').on('click', function() {
        closeModals();
    });

    // Search templates function
    function searchTemplates() {
        const searchTerm = $('#template-search').val();
        const category = $('#template-category').val();

        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_search_templates',
                nonce: dab_templates.nonce,
                search: searchTerm,
                category: category
            },
            beforeSend: function() {
                $('#search-templates').addClass('loading');
            },
            success: function(response) {
                if (response.success) {
                    updateTemplateGrid(response.data.templates);
                } else {
                    showNotice('error', response.data || 'Search failed');
                }
            },
            error: function() {
                showNotice('error', 'Search request failed');
            },
            complete: function() {
                $('#search-templates').removeClass('loading');
            }
        });
    }

    // Update template grid
    function updateTemplateGrid(templates) {
        const grid = $('#templates-grid');
        grid.empty();

        if (templates.length === 0) {
            grid.html(`
                <div class="dab-no-templates">
                    <div class="dab-no-templates-icon">
                        <span class="dashicons dashicons-admin-appearance"></span>
                    </div>
                    <h3>No Templates Found</h3>
                    <p>No templates match your search criteria. Try adjusting your filters.</p>
                </div>
            `);
            return;
        }

        templates.forEach(function(template) {
            const templateCard = createTemplateCard(template);
            grid.append(templateCard);
        });
    }

    // Create template card HTML
    function createTemplateCard(template) {
        const installCount = template.install_count || 0;
        const rating = template.rating || 0;
        const icon = template.icon || 'dashicons-admin-generic';
        const category = template.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        return `
            <div class="dab-template-card" data-category="${template.category}">
                <div class="dab-template-header">
                    <div class="dab-template-icon">
                        <span class="dashicons ${icon}"></span>
                    </div>
                    <div class="dab-template-meta">
                        <h3 class="dab-template-title">${template.name}</h3>
                        <span class="dab-template-category">${category}</span>
                    </div>
                </div>
                
                <div class="dab-template-body">
                    <p class="dab-template-description">${template.description}</p>
                    
                    <div class="dab-template-stats">
                        <span class="dab-install-count">
                            <span class="dashicons dashicons-download"></span>
                            ${installCount} installs
                        </span>
                        ${rating > 0 ? `
                            <span class="dab-rating">
                                <span class="dashicons dashicons-star-filled"></span>
                                ${rating.toFixed(1)}
                            </span>
                        ` : ''}
                    </div>
                </div>
                
                <div class="dab-template-footer">
                    <button type="button" class="dab-btn dab-btn-outline-primary dab-preview-template" 
                            data-template-id="${template.id}">
                        <span class="dashicons dashicons-visibility"></span>
                        Preview
                    </button>
                    
                    <button type="button" class="dab-btn dab-btn-primary dab-install-template" 
                            data-template-id="${template.id}"
                            data-template-name="${template.name}">
                        <span class="dashicons dashicons-download"></span>
                        Install
                    </button>
                </div>
            </div>
        `;
    }

    // Preview template
    function previewTemplate(templateId) {
        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_get_template_preview',
                nonce: dab_templates.nonce,
                template_id: templateId
            },
            beforeSend: function() {
                showModal('#dab-template-preview-modal');
                $('#dab-preview-content').html('<div class="dab-loading">Loading preview...</div>');
            },
            success: function(response) {
                if (response.success) {
                    currentTemplateId = templateId;
                    $('#dab-preview-title').text('Template Preview - ' + response.data.template.name);
                    $('#dab-preview-content').html(response.data.preview_html);
                } else {
                    $('#dab-preview-content').html('<div class="dab-error">Failed to load preview</div>');
                }
            },
            error: function() {
                $('#dab-preview-content').html('<div class="dab-error">Preview request failed</div>');
            }
        });
    }

    // Show install modal
    function showInstallModal(templateId, templateName) {
        currentTemplateId = templateId;
        $('#installation-name').val('');
        $('#installation-progress').hide();
        showModal('#dab-template-install-modal');
        
        // Suggest installation name based on template
        const suggestedName = templateName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 20);
        $('#installation-name').attr('placeholder', 'e.g., ' + suggestedName);
    }

    // Install template
    function installTemplate(templateId, installationName) {
        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_install_template',
                nonce: dab_templates.nonce,
                template_id: templateId,
                installation_name: installationName
            },
            beforeSend: function() {
                $('#dab-confirm-install').prop('disabled', true).addClass('loading');
                $('#installation-progress').show();
                $('.dab-progress-text').text(dab_templates.messages.installing);
            },
            success: function(response) {
                if (response.success) {
                    $('.dab-progress-text').text(dab_templates.messages.install_success);
                    showNotice('success', response.data.message);
                    
                    setTimeout(function() {
                        closeModals();
                        if (response.data.redirect_url) {
                            window.location.href = response.data.redirect_url;
                        } else {
                            location.reload();
                        }
                    }, 2000);
                } else {
                    $('.dab-progress-text').text('Installation failed');
                    showNotice('error', response.data || dab_templates.messages.install_error);
                }
            },
            error: function() {
                $('.dab-progress-text').text('Installation failed');
                showNotice('error', dab_templates.messages.install_error);
            },
            complete: function() {
                $('#dab-confirm-install').prop('disabled', false).removeClass('loading');
            }
        });
    }

    // Delete installation
    function deleteInstallation(installationId) {
        $.ajax({
            url: dab_templates.ajax_url,
            type: 'POST',
            data: {
                action: 'dab_delete_template_installation',
                nonce: dab_templates.nonce,
                installation_id: installationId
            },
            beforeSend: function() {
                $(`.dab-delete-installation[data-installation-id="${installationId}"]`).addClass('loading');
            },
            success: function(response) {
                if (response.success) {
                    showNotice('success', response.data.message);
                    $(`.dab-delete-installation[data-installation-id="${installationId}"]`).closest('.dab-installed-card').fadeOut();
                } else {
                    showNotice('error', response.data || 'Failed to delete installation');
                }
            },
            error: function() {
                showNotice('error', 'Delete request failed');
            },
            complete: function() {
                $(`.dab-delete-installation[data-installation-id="${installationId}"]`).removeClass('loading');
            }
        });
    }

    // Show modal
    function showModal(modalSelector) {
        $('.dab-modal-overlay').show();
        $(modalSelector).show();
    }

    // Close modals
    function closeModals() {
        $('.dab-modal').hide();
        $('.dab-modal-overlay').hide();
        currentTemplateId = null;
    }

    // Show notice
    function showNotice(type, message) {
        const noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
        const notice = $(`
            <div class="notice ${noticeClass} is-dismissible">
                <p>${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);
        
        $('.dab-admin-header').after(notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            notice.fadeOut();
        }, 5000);
        
        // Manual dismiss
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut();
        });
    }
});
