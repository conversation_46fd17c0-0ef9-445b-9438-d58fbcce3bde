<?php
/**
 * Data Dashboard Manager
 *
 * Handles the data management dashboard functionality.
 */
class DAB_Data_Dashboard_Manager {

    /**
     * Initialize the class
     */
    public static function init() {
        // Register AJAX handlers
        add_action('wp_ajax_dab_get_dashboard_table_data', array(__CLASS__, 'ajax_get_table_data'));
        add_action('wp_ajax_dab_get_dashboard_table_fields', array(__CLASS__, 'ajax_get_table_fields'));
        add_action('wp_ajax_dab_get_dashboard_record', array(__CLASS__, 'ajax_get_record'));
        add_action('wp_ajax_dab_save_dashboard_record', array(__CLASS__, 'ajax_save_record'));
        add_action('wp_ajax_dab_delete_dashboard_record', array(__CLASS__, 'ajax_delete_record'));
        add_action('wp_ajax_dab_get_dashboard_chart_data', array(__CLASS__, 'ajax_get_chart_data'));

        // New AJAX handlers for bulk operations and import/export
        add_action('wp_ajax_dab_bulk_delete_records', array(__CLASS__, 'ajax_bulk_delete_records'));
        add_action('wp_ajax_dab_export_selected_records', array(__CLASS__, 'ajax_export_selected_records'));
        add_action('wp_ajax_dab_export_data', array(__CLASS__, 'ajax_export_data'));
        add_action('wp_ajax_dab_import_data', array(__CLASS__, 'ajax_import_data'));
        add_action('wp_ajax_dab_bulk_workflow_action', array(__CLASS__, 'ajax_bulk_workflow_action'));
    }

    /**
     * AJAX handler for getting table data
     */
    public static function ajax_get_table_data() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check table ID
        if (!isset($_POST['table_id'])) {
            wp_send_json_error('No table ID provided');
            return;
        }

        $table_id = intval($_POST['table_id']);
        $filters = isset($_POST['filters']) ? $_POST['filters'] : array();

        // Get table data
        $data = self::get_table_data($table_id, $filters);

        wp_send_json_success($data);
    }

    /**
     * AJAX handler for getting table fields
     */
    public static function ajax_get_table_fields() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check table ID
        if (!isset($_POST['table_id'])) {
            wp_send_json_error('No table ID provided');
            return;
        }

        $table_id = intval($_POST['table_id']);

        // Check if user has permission to view this table
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
            $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $table_id);
            if (!$can_view) {

                wp_send_json_error('You do not have permission to view this table');
                return;
            }
        }

        // Get table fields
        $fields = DAB_Data_Manager::get_fields($table_id);



        wp_send_json_success($fields);
    }

    /**
     * AJAX handler for getting a record
     */
    public static function ajax_get_record() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check table ID and record ID
        if (!isset($_POST['table_id']) || !isset($_POST['record_id'])) {
            wp_send_json_error('Missing required parameters');
        }

        $table_id = intval($_POST['table_id']);
        $record_id = intval($_POST['record_id']);

        // Get record
        $record = self::get_record($table_id, $record_id);

        if (!$record) {
            wp_send_json_error('Record not found');
        }

        wp_send_json_success($record);
    }

    /**
     * AJAX handler for saving a record
     */
    public static function ajax_save_record() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check table ID
        if (!isset($_POST['table_id'])) {
            wp_send_json_error('No table ID provided');
        }

        $table_id = intval($_POST['table_id']);
        $record_id = isset($_POST['record_id']) && !empty($_POST['record_id']) ? intval($_POST['record_id']) : null;

        // Get fields
        $fields = DAB_Data_Manager::get_fields($table_id);

        // Prepare data
        $data = array();
        foreach ($fields as $field) {
            $field_slug = $field->field_slug;

            // Skip fields that shouldn't be directly edited
            if (in_array($field->field_type, array('formula', 'rollup'))) {
                continue;
            }

            // Handle checkboxes (they're not sent if unchecked)
            if ($field->field_type === 'checkbox') {
                $data[$field_slug] = isset($_POST[$field_slug]) ? 1 : 0;
                continue;
            }

            if (isset($_POST[$field_slug])) {
                $data[$field_slug] = sanitize_text_field($_POST[$field_slug]);
            }
        }

        // Add user ID if not set
        if (!isset($data['user_id'])) {
            $data['user_id'] = get_current_user_id();
        }

        // Add timestamps
        $data['updated_at'] = current_time('mysql');

        if (!$record_id) {
            $data['created_at'] = current_time('mysql');
        }

        // Save record
        $result = self::save_record($table_id, $record_id, $data);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success(array(
            'message' => $record_id ? 'Record updated successfully' : 'Record created successfully',
            'record_id' => $result
        ));
    }

    /**
     * AJAX handler for deleting a record
     */
    public static function ajax_delete_record() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check table ID and record ID
        if (!isset($_POST['table_id']) || !isset($_POST['record_id'])) {
            wp_send_json_error('Missing required parameters');
        }

        $table_id = intval($_POST['table_id']);
        $record_id = intval($_POST['record_id']);

        // Delete record
        $result = DAB_Data_Manager::delete_record($table_id, $record_id);

        if (!$result) {
            wp_send_json_error('Failed to delete record');
        }

        wp_send_json_success('Record deleted successfully');
    }

    /**
     * AJAX handler for getting chart data
     */
    public static function ajax_get_chart_data() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
        }

        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['chart_type']) || !isset($_POST['x_axis']) || !isset($_POST['y_axis'])) {
            wp_send_json_error('Missing required parameters');
        }

        $table_id = intval($_POST['table_id']);
        $chart_type = sanitize_text_field($_POST['chart_type']);
        $x_axis = sanitize_text_field($_POST['x_axis']);
        $y_axis = sanitize_text_field($_POST['y_axis']);
        $filters = isset($_POST['filters']) ? $_POST['filters'] : array();

        // Check if user has permission to view this table
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
            $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $table_id);
            if (!$can_view) {
                wp_send_json_error('You do not have permission to view this table');
                return;
            }
        }

        // Get chart data
        $data = self::get_chart_data($table_id, $chart_type, $x_axis, $y_axis, $filters);

        wp_send_json_success($data);
    }

    /**
     * Get table data
     *
     * @param int $table_id The table ID
     * @param array $filters Optional filters
     * @return array The table data
     */
    public static function get_table_data($table_id, $filters = array()) {
        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            return array('error' => 'Table not found');
        }

        // Check if user has permission to view this table
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
            $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $table_id);
            if (!$can_view) {
                return array('error' => 'You do not have permission to view this table');
            }
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Get fields
        $fields = DAB_Data_Manager::get_fields($table_id);

        // Build headers
        $headers = array();
        foreach ($fields as $field) {
            $headers[] = array(
                'label' => $field->field_label,
                'slug' => $field->field_slug,
                'type' => $field->field_type
            );
        }

        // Build query
        $query = "SELECT * FROM $data_table";

        // Add filters
        if (!empty($filters)) {
            $where_clauses = array();

            foreach ($filters as $filter) {
                $field = sanitize_text_field($filter['field']);
                $operator = sanitize_text_field($filter['operator']);
                $value = sanitize_text_field($filter['value']);

                switch ($operator) {
                    case 'equals':
                        $where_clauses[] = $wpdb->prepare("$field = %s", $value);
                        break;
                    case 'not_equals':
                        $where_clauses[] = $wpdb->prepare("$field != %s", $value);
                        break;
                    case 'contains':
                        $where_clauses[] = $wpdb->prepare("$field LIKE %s", '%' . $wpdb->esc_like($value) . '%');
                        break;
                    case 'starts_with':
                        $where_clauses[] = $wpdb->prepare("$field LIKE %s", $wpdb->esc_like($value) . '%');
                        break;
                    case 'ends_with':
                        $where_clauses[] = $wpdb->prepare("$field LIKE %s", '%' . $wpdb->esc_like($value));
                        break;
                    case 'greater_than':
                        $where_clauses[] = $wpdb->prepare("$field > %s", $value);
                        break;
                    case 'less_than':
                        $where_clauses[] = $wpdb->prepare("$field < %s", $value);
                        break;
                }
            }

            if (!empty($where_clauses)) {
                $query .= " WHERE " . implode(" AND ", $where_clauses);
            }
        }

        // Add order by
        $query .= " ORDER BY id DESC";

        // Get records
        $records = $wpdb->get_results($query, ARRAY_A);

        return array(
            'headers' => $headers,
            'records' => $records
        );
    }

    /**
     * Get a record
     *
     * @param int $table_id The table ID
     * @param int $record_id The record ID
     * @return object|false The record or false if not found
     */
    public static function get_record($table_id, $record_id) {
        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            return false;
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if user has permission to view this record
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
            $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $table_id);
            if (!$can_view) {
                // Check if user is the owner of the record
                $owner_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT user_id FROM $data_table WHERE id = %d", $record_id
                ));

                if ($owner_id != $current_user_id) {
                    return false; // User doesn't have permission to view this record
                }
            }
        }

        // Get record
        $record = $wpdb->get_row($wpdb->prepare("SELECT * FROM $data_table WHERE id = %d", $record_id), ARRAY_A);

        return $record;
    }

    /**
     * Save a record
     *
     * @param int $table_id The table ID
     * @param int|null $record_id The record ID (null for new record)
     * @param array $data The record data
     * @return int|WP_Error The record ID or WP_Error on failure
     */
    public static function save_record($table_id, $record_id, $data) {
        global $wpdb;

        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {

            return new WP_Error('table_not_found', 'Table not found');
        }



        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check permissions if updating an existing record
        if ($record_id) {
            $current_user_id = get_current_user_id();
            $user = wp_get_current_user();
            $is_admin = in_array('administrator', $user->roles);

            if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
                $can_edit = DAB_Role_Permissions_Manager::can_user_edit_records($current_user_id, $table_id, $record_id);
                if (!$can_edit) {
                    // Check if user is the owner of the record
                    $owner_id = $wpdb->get_var($wpdb->prepare(
                        "SELECT user_id FROM $data_table WHERE id = %d", $record_id
                    ));

                    if ($owner_id != $current_user_id) {
                        return new WP_Error('permission_denied', 'You do not have permission to edit this record');
                    }
                }
            }
        }

        // Insert or update record
        if ($record_id) {
            // Update existing record
            $result = $wpdb->update($data_table, $data, array('id' => $record_id));

            if ($result === false) {
                return new WP_Error('update_failed', 'Failed to update record: ' . $wpdb->last_error);
            }

            return $record_id;
        } else {
            // For new records, add the current user ID if not already set
            if (!isset($data['user_id'])) {
                $data['user_id'] = get_current_user_id();
            }

            // Insert new record
            $result = $wpdb->insert($data_table, $data);

            if ($result === false) {
                return new WP_Error('insert_failed', 'Failed to insert record: ' . $wpdb->last_error);
            }

            $new_record_id = $wpdb->insert_id;

            // Initialize approval workflow if enabled
            if (class_exists('DAB_Approval_Manager')) {
                DAB_Approval_Manager::initialize_approval($table_id, $new_record_id);
            }

            return $new_record_id;
        }
    }

    /**
     * Get chart data
     *
     * @param int $table_id The table ID
     * @param string $chart_type The chart type
     * @param string $x_axis The X-axis field
     * @param string $y_axis The Y-axis field
     * @param array $filters Optional filters
     * @return array The chart data
     */
    public static function get_chart_data($table_id, $chart_type, $x_axis, $y_axis, $filters = array()) {
        global $wpdb;



        // Get table info
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            error_log('Table not found with ID: ' . $table_id);
            return array('error' => 'Table not found');
        }

        // Check if user has permission to view this table
        $current_user_id = get_current_user_id();
        $user = wp_get_current_user();
        $is_admin = in_array('administrator', $user->roles);

        if (!$is_admin && class_exists('DAB_Role_Permissions_Manager')) {
            $can_view = DAB_Role_Permissions_Manager::can_user_view_records($current_user_id, $table_id);
            if (!$can_view) {
                error_log('User does not have permission to view table ID ' . $table_id);
                return array('error' => 'You do not have permission to view this table');
            }
        }

        error_log('Found table: ' . $table_info->table_label);

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$data_table'") === $data_table;
        if (!$table_exists) {
            error_log('Data table does not exist: ' . $data_table);
            return array('error' => 'Data table does not exist');
        }

        // Get fields
        $fields = DAB_Data_Manager::get_fields($table_id);

        if (empty($fields)) {
            error_log('No fields found for table ID: ' . $table_id);
            return array('error' => 'No fields found for this table');
        }

        // Find field labels
        $x_axis_label = '';
        $y_axis_label = '';
        $x_field_exists = false;
        $y_field_exists = false;

        foreach ($fields as $field) {
            if ($field->field_slug === $x_axis) {
                $x_axis_label = $field->field_label;
                $x_field_exists = true;
            }

            if ($field->field_slug === $y_axis) {
                $y_axis_label = $field->field_label;
                $y_field_exists = true;
            }
        }

        if (!$x_field_exists) {
            return array('error' => 'X-axis field not found: ' . $x_axis);
        }

        if (!$y_field_exists) {
            return array('error' => 'Y-axis field not found: ' . $y_axis);
        }

        // Build query
        $query = "SELECT $x_axis, SUM($y_axis) as total FROM $data_table";

        // Add filters
        if (!empty($filters)) {
            $where_clauses = array();

            foreach ($filters as $filter) {
                $field = sanitize_text_field($filter['field']);
                $operator = sanitize_text_field($filter['operator']);
                $value = sanitize_text_field($filter['value']);

                switch ($operator) {
                    case 'equals':
                        $where_clauses[] = $wpdb->prepare("$field = %s", $value);
                        break;
                    case 'not_equals':
                        $where_clauses[] = $wpdb->prepare("$field != %s", $value);
                        break;
                    case 'contains':
                        $where_clauses[] = $wpdb->prepare("$field LIKE %s", '%' . $wpdb->esc_like($value) . '%');
                        break;
                    case 'starts_with':
                        $where_clauses[] = $wpdb->prepare("$field LIKE %s", $wpdb->esc_like($value) . '%');
                        break;
                    case 'ends_with':
                        $where_clauses[] = $wpdb->prepare("$field LIKE %s", '%' . $wpdb->esc_like($value));
                        break;
                    case 'greater_than':
                        $where_clauses[] = $wpdb->prepare("$field > %s", $value);
                        break;
                    case 'less_than':
                        $where_clauses[] = $wpdb->prepare("$field < %s", $value);
                        break;
                }
            }

            if (!empty($where_clauses)) {
                $query .= " WHERE " . implode(" AND ", $where_clauses);
            }
        }

        // Group by X-axis
        $query .= " GROUP BY $x_axis";

        // Get data
        $results = $wpdb->get_results($query);

        if ($wpdb->last_error) {
            return array('error' => 'Database error: ' . $wpdb->last_error);
        }

        if (empty($results)) {
            return array('error' => 'No data found for the selected fields');
        }



        // Prepare chart data
        $labels = array();
        $values = array();

        foreach ($results as $result) {
            // Handle null values
            $label_value = isset($result->$x_axis) ? $result->$x_axis : 'Unknown';
            $total_value = isset($result->total) ? (float) $result->total : 0;

            $labels[] = $label_value;
            $values[] = $total_value;
        }

        error_log('Chart labels: ' . print_r($labels, true));
        error_log('Chart values: ' . print_r($values, true));

        return array(
            'labels' => $labels,
            'values' => $values,
            'label' => $y_axis_label,
            'chart_type' => $chart_type
        );
    }

    /**
     * AJAX handler for bulk deleting records
     */
    public static function ajax_bulk_delete_records() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['record_ids']) || !is_array($_POST['record_ids'])) {
            wp_send_json_error('Missing required parameters');
            return;
        }

        $table_id = intval($_POST['table_id']);
        $record_ids = array_map('intval', $_POST['record_ids']);

        if (empty($record_ids)) {
            wp_send_json_error('No records selected');
            return;
        }

        // Delete records
        $success_count = 0;
        $error_count = 0;

        foreach ($record_ids as $record_id) {
            $result = DAB_Data_Manager::delete_record($table_id, $record_id);
            if ($result) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        if ($error_count > 0) {
            if ($success_count > 0) {
                wp_send_json_success(array(
                    'message' => sprintf('%d records deleted successfully, %d failed', $success_count, $error_count)
                ));
            } else {
                wp_send_json_error('Failed to delete records');
            }
        } else {
            wp_send_json_success(array(
                'message' => sprintf('%d records deleted successfully', $success_count)
            ));
        }
    }

    /**
     * AJAX handler for exporting selected records
     */
    public static function ajax_export_selected_records() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['record_ids']) || !is_array($_POST['record_ids']) || !isset($_POST['format'])) {
            wp_send_json_error('Missing required parameters');
            return;
        }

        $table_id = intval($_POST['table_id']);
        $record_ids = array_map('intval', $_POST['record_ids']);
        $format = sanitize_text_field($_POST['format']);

        if (empty($record_ids)) {
            wp_send_json_error('No records selected');
            return;
        }

        if (!in_array($format, array('csv', 'json'))) {
            wp_send_json_error('Invalid export format');
            return;
        }

        // Get table info
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            wp_send_json_error('Table not found');
            return;
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Get fields
        $fields = DAB_Data_Manager::get_fields($table_id);

        // Build query with record IDs
        $placeholders = implode(',', array_fill(0, count($record_ids), '%d'));
        $query = $wpdb->prepare("SELECT * FROM $data_table WHERE id IN ($placeholders)", $record_ids);
        $records = $wpdb->get_results($query, ARRAY_A);

        if (empty($records)) {
            wp_send_json_error('No records found');
            return;
        }

        // Set filename
        $date = date('Y-m-d');
        $filename = sanitize_file_name("{$table_info->table_label}_export_{$date}." . $format);

        // Export based on format
        if ($format === 'csv') {
            // Generate CSV content
            $csv_content = self::generate_csv_content($fields, $records);

            // Return CSV content
            wp_send_json_success(array(
                'content' => $csv_content,
                'filename' => $filename
            ));
        } else {
            // Generate JSON content
            $json_content = self::generate_json_content($fields, $records);

            // Return JSON content
            wp_send_json_success(array(
                'content' => $json_content,
                'filename' => $filename
            ));
        }
    }

    /**
     * AJAX handler for exporting all data
     */
    public static function ajax_export_data() {
        // Check nonce
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'dab_data_nonce')) {
            wp_die('Invalid nonce');
            return;
        }

        // Check required parameters
        if (!isset($_REQUEST['table_id']) || !isset($_REQUEST['format'])) {
            wp_die('Missing required parameters');
            return;
        }

        $table_id = intval($_REQUEST['table_id']);
        $format = sanitize_text_field($_REQUEST['format']);

        if (!in_array($format, array('csv', 'json'))) {
            wp_die('Invalid export format');
            return;
        }

        // Get table info
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            wp_die('Table not found');
            return;
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Get fields
        $fields = DAB_Data_Manager::get_fields($table_id);

        // Get records
        $records = $wpdb->get_results("SELECT * FROM $data_table ORDER BY id DESC", ARRAY_A);

        if (empty($records)) {
            wp_die('No records found');
            return;
        }

        // Set filename
        $date = date('Y-m-d');
        $filename = sanitize_file_name("{$table_info->table_label}_export_{$date}." . $format);

        // Export based on format
        if ($format === 'csv') {
            // Set headers for CSV download
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            // Output CSV content
            echo self::generate_csv_content($fields, $records);
        } else {
            // Set headers for JSON download
            header('Content-Type: application/json; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            // Output JSON content
            echo self::generate_json_content($fields, $records);
        }

        exit;
    }

    /**
     * AJAX handler for importing data
     */
    public static function ajax_import_data() {
        // Debug log
        error_log('AJAX: dab_import_data called');
        error_log('POST data: ' . print_r($_POST, true));
        error_log('FILES data: ' . print_r($_FILES, true));

        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            error_log('AJAX: Invalid nonce');
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_FILES['file'])) {
            error_log('AJAX: Missing required parameters');
            wp_send_json_error('Missing required parameters');
            return;
        }

        $table_id = intval($_POST['table_id']);
        $overwrite = isset($_POST['overwrite']) && $_POST['overwrite'] === '1';
        $skip_header = isset($_POST['skip_header']) && $_POST['skip_header'] === '1';

        // Check file
        $file = $_FILES['file'];
        if ($file['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error('File upload error: ' . $file['error']);
            return;
        }

        // Check file type
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_ext, array('csv', 'json'))) {
            error_log('AJAX: Invalid file type: ' . $file_ext);
            wp_send_json_error('Invalid file type. Please upload a CSV or JSON file.');
            return;
        }

        error_log('File details - Name: ' . $file['name'] . ', Type: ' . $file['type'] . ', Size: ' . $file['size'] . ' bytes');

        // Get table info
        global $wpdb;
        $tables_table = $wpdb->prefix . 'dab_tables';
        $table_info = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $table_id));

        if (!$table_info) {
            error_log('AJAX: Table not found for ID: ' . $table_id);
            wp_send_json_error('Table not found');
            return;
        }

        // Get data table name
        $data_table = $wpdb->prefix . 'dab_' . sanitize_title($table_info->table_slug);

        // Get fields
        $fields = DAB_Data_Manager::get_fields($table_id);
        $field_slugs = array_map(function($field) {
            return $field->field_slug;
        }, $fields);

        // Read file content
        $file_content = file_get_contents($file['tmp_name']);
        if ($file_content === false) {
            wp_send_json_error('Failed to read file');
            return;
        }

        // Parse file content
        $records = array();
        if ($file_ext === 'csv') {
            $records = self::parse_csv_content($file_content, $skip_header);
        } else {
            $records = self::parse_json_content($file_content);
        }

        if (empty($records)) {
            wp_send_json_error('No records found in file');
            return;
        }

        // Import records
        $success_count = 0;
        $error_count = 0;
        $imported_ids = array();



        // Try to map fields if headers don't match exactly
        $field_mapping = array();
        if (!empty($records) && is_array($records[0])) {
            $first_record_keys = array_keys($records[0]);

            // Check if we need to do field mapping (if keys don't match field slugs)
            $needs_mapping = false;
            foreach ($first_record_keys as $key) {
                if (!in_array($key, $field_slugs)) {
                    $needs_mapping = true;
                    break;
                }
            }

            if ($needs_mapping) {
                error_log('Field mapping needed - CSV headers don\'t match field slugs');

                // Try to map based on similarity
                foreach ($first_record_keys as $key) {
                    // Skip if already a valid field
                    if (in_array($key, $field_slugs)) {
                        $field_mapping[$key] = $key;
                        continue;
                    }

                    // Try to find a match
                    $best_match = null;
                    $best_score = 0;

                    foreach ($field_slugs as $field_slug) {
                        // Calculate similarity score
                        $score = 0;

                        // Exact match after normalization
                        $key_string = is_null($key) ? '' : (string)$key;
                        $slug_string = is_null($field_slug) ? '' : (string)$field_slug;
                        $normalized_key = strtolower(str_replace(['_', '-', ' '], '', $key_string));
                        $normalized_slug = strtolower(str_replace(['_', '-', ' '], '', $slug_string));

                        if ($normalized_key === $normalized_slug) {
                            $score = 100;
                        } else {
                            // Partial match
                            similar_text($normalized_key, $normalized_slug, $score);
                        }

                        if ($score > $best_score && $score > 70) { // 70% similarity threshold
                            $best_score = $score;
                            $best_match = $field_slug;
                        }
                    }

                    if ($best_match) {
                        $field_mapping[$key] = $best_match;

                    }
                }
            } else {
                // No mapping needed, use direct mapping
                foreach ($field_slugs as $slug) {
                    $field_mapping[$slug] = $slug;
                }
            }
        }



        foreach ($records as $index => $record) {
            // Filter record to only include valid fields
            $record_data = array();

            foreach ($record as $key => $value) {
                // Check if we have a mapping for this key
                $mapped_key = isset($field_mapping[$key]) ? $field_mapping[$key] : null;

                if ($mapped_key && in_array($mapped_key, $field_slugs)) {
                    $record_data[$mapped_key] = $value;
                }
            }

            if (empty($record_data)) {

                $error_count++;
                continue;
            }

            // Add timestamps
            $record_data['updated_at'] = current_time('mysql');

            // Check if record has ID and overwrite is enabled
            if (isset($record['id']) && $overwrite) {
                $record_id = intval($record['id']);
                $exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM $data_table WHERE id = %d", $record_id));

                if ($exists) {
                    // Update existing record
                    $result = $wpdb->update($data_table, $record_data, array('id' => $record_id));
                    if ($result !== false) {
                        $success_count++;
                        $imported_ids[] = $record_id;

                    } else {
                        $error_count++;

                    }
                    continue;
                }
            }

            // Insert new record
            $record_data['created_at'] = current_time('mysql');
            $result = $wpdb->insert($data_table, $record_data);
            if ($result) {
                $new_id = $wpdb->insert_id;
                $success_count++;
                $imported_ids[] = $new_id;

            } else {
                $error_count++;

            }
        }



        // Prepare response data
        $response_data = array(
            'message' => sprintf('%d records imported successfully', $success_count),
            'success_count' => $success_count,
            'error_count' => $error_count,
            'imported_ids' => $imported_ids
        );

        if ($error_count > 0) {
            if ($success_count > 0) {
                $response_data['message'] = sprintf('%d records imported successfully, %d failed', $success_count, $error_count);
                wp_send_json_success($response_data);
            } else {
                wp_send_json_error('Failed to import records');
            }
        } else {
            wp_send_json_success($response_data);
        }
    }

    /**
     * AJAX handler for bulk workflow actions
     */
    public static function ajax_bulk_workflow_action() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_data_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check required parameters
        if (!isset($_POST['table_id']) || !isset($_POST['record_ids']) || !is_array($_POST['record_ids']) || !isset($_POST['workflow_action'])) {
            wp_send_json_error('Missing required parameters');
            return;
        }

        $table_id = intval($_POST['table_id']);
        $record_ids = array_map('intval', $_POST['record_ids']);
        $action = sanitize_text_field($_POST['workflow_action']);
        $reason = isset($_POST['reason']) ? sanitize_textarea_field($_POST['reason']) : '';

        if (empty($record_ids)) {
            wp_send_json_error('No records selected');
            return;
        }

        if (!in_array($action, array('approve', 'reject'))) {
            wp_send_json_error('Invalid workflow action');
            return;
        }

        // Check if approval manager exists
        if (!class_exists('DAB_Approval_Manager')) {
            wp_send_json_error('Approval system is not available');
            return;
        }

        // Process workflow actions
        $success_count = 0;
        $error_count = 0;
        $current_user_id = get_current_user_id();

        foreach ($record_ids as $record_id) {
            if ($action === 'approve') {
                $result = DAB_Approval_Manager::approve_record($table_id, $record_id, $current_user_id, $reason);
            } else {
                $result = DAB_Approval_Manager::reject_record($table_id, $record_id, $current_user_id, $reason);
            }

            if ($result) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        if ($error_count > 0) {
            if ($success_count > 0) {
                wp_send_json_success(array(
                    'message' => sprintf('%d records %s successfully, %d failed',
                        $success_count,
                        $action === 'approve' ? 'approved' : 'rejected',
                        $error_count
                    )
                ));
            } else {
                wp_send_json_error(sprintf('Failed to %s records', $action === 'approve' ? 'approve' : 'reject'));
            }
        } else {
            wp_send_json_success(array(
                'message' => sprintf('%d records %s successfully',
                    $success_count,
                    $action === 'approve' ? 'approved' : 'rejected'
                )
            ));
        }
    }

    /**
     * Generate CSV content
     *
     * @param array $fields The table fields
     * @param array $records The records to export
     * @return string The CSV content
     */
    private static function generate_csv_content($fields, $records) {
        // Create a file pointer connected to the output stream
        $output = fopen('php://temp', 'r+');

        // Add UTF-8 BOM for Excel compatibility
        fputs($output, "\xEF\xBB\xBF");

        // Add column headers
        $headers = array();
        foreach ($fields as $field) {
            $headers[] = $field->field_label;
        }
        fputcsv($output, $headers);

        // Add data rows
        foreach ($records as $record) {
            $row = array();
            foreach ($fields as $field) {
                $val = isset($record[$field->field_slug]) ? $record[$field->field_slug] : '';

                // Ensure $val is not null to avoid PHP deprecated warnings
                if ($val === null) {
                    $val = '';
                }

                // Format value based on field type
                switch ($field->field_type) {
                    case 'date':
                        $val = !empty($val) ? date('Y-m-d', strtotime($val)) : '';
                        break;
                    case 'datetime':
                        $val = !empty($val) ? date('Y-m-d H:i:s', strtotime($val)) : '';
                        break;
                    case 'boolean':
                        $val = $val ? 'Yes' : 'No';
                        break;
                    case 'lookup':
                        // Get the display value for lookup fields
                        $val = DAB_Data_Manager::get_lookup_display_value($field, $val);
                        break;
                    case 'formula':
                        // Ensure numeric formatting
                        $val = is_numeric($val) ? number_format((float)$val, 2) : $val;
                        break;
                }

                $row[] = $val;
            }
            fputcsv($output, $row);
        }

        // Get the content
        rewind($output);
        $content = stream_get_contents($output);
        fclose($output);

        return $content;
    }

    /**
     * Generate JSON content
     *
     * @param array $fields The table fields
     * @param array $records The records to export
     * @return string The JSON content
     */
    private static function generate_json_content($fields, $records) {
        // Format records for JSON export
        $formatted_records = array();
        foreach ($records as $record) {
            $formatted_record = array();
            foreach ($fields as $field) {
                $val = isset($record[$field->field_slug]) ? $record[$field->field_slug] : '';

                // Ensure $val is not null to avoid PHP deprecated warnings
                if ($val === null) {
                    $val = '';
                }

                // Format value based on field type
                switch ($field->field_type) {
                    case 'date':
                        $val = !empty($val) ? date('Y-m-d', strtotime($val)) : '';
                        break;
                    case 'datetime':
                        $val = !empty($val) ? date('Y-m-d H:i:s', strtotime($val)) : '';
                        break;
                    case 'boolean':
                        $val = (bool)$val;
                        break;
                    case 'number':
                    case 'currency':
                    case 'formula':
                        $val = is_numeric($val) ? (float)$val : $val;
                        break;
                    case 'lookup':
                        // Get the display value for lookup fields
                        $val = DAB_Data_Manager::get_lookup_display_value($field, $val);
                        break;
                }

                $formatted_record[$field->field_slug] = $val;
            }
            $formatted_records[] = $formatted_record;
        }

        return json_encode($formatted_records, JSON_PRETTY_PRINT);
    }

    /**
     * Parse CSV content
     *
     * @param string $content The CSV content
     * @param bool $skip_header Whether to skip the header row
     * @return array The parsed records
     */
    private static function parse_csv_content($content, $skip_header = true) {


        $records = array();

        // Handle different line endings
        $content_string = is_null($content) ? '' : (string)$content;
        $content_string = str_replace("\r\n", "\n", $content_string);
        $content_string = str_replace("\r", "\n", $content_string);
        $lines = explode("\n", $content_string);

        // Remove BOM if present
        if (substr($lines[0], 0, 3) === "\xEF\xBB\xBF") {
            $lines[0] = substr($lines[0], 3);
        }

        // Get headers
        if (empty($lines[0])) {
            return array();
        }

        $headers = str_getcsv($lines[0]);
        $headers = array_map('trim', $headers);



        // Parse data rows
        $start_index = $skip_header ? 1 : 0;
        $success_count = 0;
        $error_count = 0;

        for ($i = $start_index; $i < count($lines); $i++) {
            if (empty(trim($lines[$i]))) continue;

            $values = str_getcsv($lines[$i]);

            // Handle case where values count doesn't match headers count
            if (count($values) !== count($headers)) {


                // If we have more values than headers, truncate
                if (count($values) > count($headers)) {
                    $values = array_slice($values, 0, count($headers));
                }
                // If we have fewer values than headers, pad with empty strings
                else if (count($values) < count($headers)) {
                    $values = array_pad($values, count($headers), '');
                }
            }

            $record = array();
            foreach ($values as $j => $value) {
                if (isset($headers[$j])) {
                    // Trim whitespace and remove any BOM characters
                    $value = trim($value);
                    if ($j === 0 && substr($value, 0, 3) === "\xEF\xBB\xBF") {
                        $value = substr($value, 3);
                    }

                    $record[$headers[$j]] = $value;
                }
            }

            if (!empty($record)) {
                $records[] = $record;
                $success_count++;
            } else {
                $error_count++;
            }
        }



        return $records;
    }

    /**
     * Parse JSON content
     *
     * @param string $content The JSON content
     * @return array The parsed records
     */
    private static function parse_json_content($content) {
        $data = json_decode($content, true);

        if (!is_array($data)) {
            return array();
        }

        return $data;
    }
}
