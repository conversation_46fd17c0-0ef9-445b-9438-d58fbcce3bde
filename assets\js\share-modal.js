/**
 * Share Modal JavaScript
 *
 * Handles sharing functionality for Database App Builder
 * This file prevents 404 errors and provides basic sharing functionality
 */

(function($) {
    'use strict';

    // Wait for DOM to be ready
    $(document).ready(function() {
        initShareModal();
    });

    /**
     * Initialize share modal functionality
     */
    function initShareModal() {
        // Check if share modal elements exist before adding event listeners
        const shareButtons = document.querySelectorAll('.dab-share-btn, .share-btn, [data-action="share"]');
        const shareModal = document.getElementById('share-modal') || document.querySelector('.share-modal');

        if (shareButtons.length === 0 && !shareModal) {
            // No share functionality needed, exit gracefully
            return;
        }

        // Add event listeners for share buttons
        shareButtons.forEach(function(button) {
            if (button && typeof button.addEventListener === 'function') {
                button.addEventListener('click', handleShareClick, { passive: true });
            }
        });

        // Add event listeners for modal close buttons
        const closeButtons = document.querySelectorAll('.share-modal-close, .dab-share-modal-close');
        closeButtons.forEach(function(button) {
            if (button && typeof button.addEventListener === 'function') {
                button.addEventListener('click', closeShareModal, { passive: true });
            }
        });

        // Close modal when clicking outside
        if (shareModal && typeof shareModal.addEventListener === 'function') {
            shareModal.addEventListener('click', function(e) {
                if (e.target === shareModal) {
                    closeShareModal();
                }
            }, { passive: true });
        }

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && isShareModalOpen()) {
                closeShareModal();
            }
        }, { passive: true });
    }

    /**
     * Handle share button click
     */
    function handleShareClick(e) {
        if (e && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }

        const button = e ? e.target : null;
        if (!button) return;

        const shareUrl = button.getAttribute('data-share-url') || window.location.href;
        const shareTitle = button.getAttribute('data-share-title') || document.title;
        const shareText = button.getAttribute('data-share-text') || '';

        // Try to use native Web Share API if available
        if (navigator.share && typeof navigator.share === 'function') {
            navigator.share({
                title: shareTitle,
                text: shareText,
                url: shareUrl
            }).catch(function(error) {
                console.log('Error sharing:', error);
                // Fallback to modal
                showShareModal(shareUrl, shareTitle, shareText);
            });
        } else {
            // Fallback to modal
            showShareModal(shareUrl, shareTitle, shareText);
        }
    }

    /**
     * Show share modal
     */
    function showShareModal(url, title, text) {
        let shareModal = document.getElementById('share-modal');

        // Create modal if it doesn't exist
        if (!shareModal) {
            shareModal = createShareModal();
        }

        // Update modal content
        updateShareModalContent(shareModal, url, title, text);

        // Show modal
        if (shareModal && typeof shareModal.style !== 'undefined') {
            shareModal.style.display = 'flex';
            shareModal.setAttribute('aria-hidden', 'false');
        }
    }

    /**
     * Create share modal HTML
     */
    function createShareModal() {
        const modal = document.createElement('div');
        modal.id = 'share-modal';
        modal.className = 'dab-share-modal';
        modal.setAttribute('role', 'dialog');
        modal.setAttribute('aria-hidden', 'true');
        modal.innerHTML = `
            <div class="dab-share-modal-content">
                <div class="dab-share-modal-header">
                    <h3>Share</h3>
                    <button type="button" class="dab-share-modal-close" aria-label="Close">&times;</button>
                </div>
                <div class="dab-share-modal-body">
                    <div class="dab-share-options">
                        <button type="button" class="dab-share-option" data-platform="copy">
                            <span class="dab-share-icon">📋</span>
                            Copy Link
                        </button>
                        <button type="button" class="dab-share-option" data-platform="email">
                            <span class="dab-share-icon">📧</span>
                            Email
                        </button>
                        <button type="button" class="dab-share-option" data-platform="facebook">
                            <span class="dab-share-icon">📘</span>
                            Facebook
                        </button>
                        <button type="button" class="dab-share-option" data-platform="twitter">
                            <span class="dab-share-icon">🐦</span>
                            Twitter
                        </button>
                        <button type="button" class="dab-share-option" data-platform="linkedin">
                            <span class="dab-share-icon">💼</span>
                            LinkedIn
                        </button>
                    </div>
                    <div class="dab-share-url-container">
                        <input type="text" id="dab-share-url-input" readonly>
                    </div>
                </div>
            </div>
        `;

        // Add event listeners to the new modal
        const closeBtn = modal.querySelector('.dab-share-modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeShareModal, { passive: true });
        }

        // Add event listeners to share options
        const shareOptions = modal.querySelectorAll('.dab-share-option');
        shareOptions.forEach(function(option) {
            option.addEventListener('click', handleShareOptionClick, { passive: true });
        });

        document.body.appendChild(modal);
        return modal;
    }

    /**
     * Update share modal content
     */
    function updateShareModalContent(modal, url, title, text) {
        if (!modal) return;

        const urlInput = modal.querySelector('#dab-share-url-input');
        if (urlInput) {
            urlInput.value = url;
        }

        // Store data for share options
        modal.setAttribute('data-share-url', url);
        modal.setAttribute('data-share-title', title);
        modal.setAttribute('data-share-text', text);
    }

    /**
     * Handle share option click
     */
    function handleShareOptionClick(e) {
        if (e && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }

        const option = e ? e.target.closest('.dab-share-option') : null;
        if (!option) return;

        const platform = option.getAttribute('data-platform');
        const modal = option.closest('.dab-share-modal');

        if (!modal) return;

        const url = modal.getAttribute('data-share-url') || '';
        const title = modal.getAttribute('data-share-title') || '';
        const text = modal.getAttribute('data-share-text') || '';

        switch (platform) {
            case 'copy':
                copyToClipboard(url);
                break;
            case 'email':
                shareViaEmail(url, title, text);
                break;
            case 'facebook':
                shareViaFacebook(url);
                break;
            case 'twitter':
                shareViaTwitter(url, title);
                break;
            case 'linkedin':
                shareViaLinkedIn(url, title);
                break;
        }
    }

    /**
     * Copy URL to clipboard
     */
    function copyToClipboard(url) {
        if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
            navigator.clipboard.writeText(url).then(function() {
                showShareFeedback('Link copied to clipboard!');
            }).catch(function() {
                fallbackCopyToClipboard(url);
            });
        } else {
            fallbackCopyToClipboard(url);
        }
    }

    /**
     * Fallback copy to clipboard
     */
    function fallbackCopyToClipboard(url) {
        const textArea = document.createElement('textarea');
        textArea.value = url;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showShareFeedback('Link copied to clipboard!');
        } catch (err) {
            showShareFeedback('Unable to copy link');
        }

        document.body.removeChild(textArea);
    }

    /**
     * Share via email
     */
    function shareViaEmail(url, title, text) {
        const subject = encodeURIComponent(title);
        const body = encodeURIComponent(text + '\n\n' + url);
        window.open(`mailto:?subject=${subject}&body=${body}`);
    }

    /**
     * Share via Facebook
     */
    function shareViaFacebook(url) {
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        openShareWindow(shareUrl);
    }

    /**
     * Share via Twitter
     */
    function shareViaTwitter(url, title) {
        const text = encodeURIComponent(title);
        const shareUrl = `https://twitter.com/intent/tweet?text=${text}&url=${encodeURIComponent(url)}`;
        openShareWindow(shareUrl);
    }

    /**
     * Share via LinkedIn
     */
    function shareViaLinkedIn(url, title) {
        const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        openShareWindow(shareUrl);
    }

    /**
     * Open share window
     */
    function openShareWindow(url) {
        const width = 600;
        const height = 400;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;

        window.open(
            url,
            'share',
            `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
        );
    }

    /**
     * Show share feedback
     */
    function showShareFeedback(message) {
        // Create or update feedback element
        let feedback = document.getElementById('dab-share-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.id = 'dab-share-feedback';
            feedback.className = 'dab-share-feedback';
            document.body.appendChild(feedback);
        }

        feedback.textContent = message;
        feedback.style.display = 'block';

        // Hide after 3 seconds
        setTimeout(function() {
            if (feedback && feedback.style) {
                feedback.style.display = 'none';
            }
        }, 3000);
    }

    /**
     * Close share modal
     */
    function closeShareModal() {
        const shareModal = document.getElementById('share-modal');
        if (shareModal && typeof shareModal.style !== 'undefined') {
            shareModal.style.display = 'none';
            shareModal.setAttribute('aria-hidden', 'true');
        }
    }

    /**
     * Check if share modal is open
     */
    function isShareModalOpen() {
        const shareModal = document.getElementById('share-modal');
        return shareModal && shareModal.style.display === 'flex';
    }

    // Add basic CSS if not already present
    if (!document.getElementById('dab-share-modal-styles')) {
        const style = document.createElement('style');
        style.id = 'dab-share-modal-styles';
        style.textContent = `
            .dab-share-modal {
                display: none;
                position: fixed;
                z-index: 10000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                align-items: center;
                justify-content: center;
            }
            .dab-share-modal-content {
                background: white;
                border-radius: 8px;
                max-width: 400px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
            }
            .dab-share-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #eee;
            }
            .dab-share-modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .dab-share-modal-body {
                padding: 20px;
            }
            .dab-share-options {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 10px;
                margin-bottom: 20px;
            }
            .dab-share-option {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background: white;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            .dab-share-option:hover {
                background-color: #f5f5f5;
            }
            .dab-share-icon {
                font-size: 24px;
                margin-bottom: 5px;
            }
            .dab-share-url-container {
                margin-top: 15px;
            }
            #dab-share-url-input {
                width: 100%;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
            .dab-share-feedback {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                z-index: 10001;
                display: none;
            }
        `;
        document.head.appendChild(style);
    }

})(jQuery);
