<?php
/**
 * Conditional Logic Engine
 *
 * Handles dynamic field visibility and form behavior based on user input
 *
 * @package Database App Builder
 * @subpackage Forms
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class DAB_Conditional_Logic_Engine {

    /**
     * Initialize the Conditional Logic Engine
     */
    public static function init() {
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
        add_action('wp_ajax_dab_evaluate_conditions', array(__CLASS__, 'evaluate_conditions'));
        add_action('wp_ajax_nopriv_dab_evaluate_conditions', array(__CLASS__, 'evaluate_conditions'));
        add_action('wp_ajax_dab_save_conditional_rules', array(__CLASS__, 'save_conditional_rules'));
        add_action('wp_ajax_dab_get_conditional_rules', array(__CLASS__, 'get_conditional_rules'));
    }

    /**
     * Enqueue scripts and styles
     */
    public static function enqueue_scripts() {
        wp_enqueue_script(
            'dab-conditional-logic',
            plugin_dir_url(__FILE__) . '../../assets/js/conditional-logic.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_localize_script('dab-conditional-logic', 'dabConditionalData', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_conditional_nonce'),
            'operators' => self::get_available_operators(),
            'i18n' => array(
                'addRule' => __('Add Rule', 'db-app-builder'),
                'deleteRule' => __('Delete Rule', 'db-app-builder'),
                'addCondition' => __('Add Condition', 'db-app-builder'),
                'deleteCondition' => __('Delete Condition', 'db-app-builder'),
                'and' => __('AND', 'db-app-builder'),
                'or' => __('OR', 'db-app-builder'),
                'show' => __('Show', 'db-app-builder'),
                'hide' => __('Hide', 'db-app-builder'),
                'enable' => __('Enable', 'db-app-builder'),
                'disable' => __('Disable', 'db-app-builder'),
                'setValue' => __('Set Value', 'db-app-builder'),
                'confirmDelete' => __('Are you sure you want to delete this rule?', 'db-app-builder')
            )
        ));
    }

    /**
     * Get available conditional operators
     */
    public static function get_available_operators() {
        return array(
            'equals' => __('equals', 'db-app-builder'),
            'not_equals' => __('does not equal', 'db-app-builder'),
            'contains' => __('contains', 'db-app-builder'),
            'not_contains' => __('does not contain', 'db-app-builder'),
            'starts_with' => __('starts with', 'db-app-builder'),
            'ends_with' => __('ends with', 'db-app-builder'),
            'is_empty' => __('is empty', 'db-app-builder'),
            'is_not_empty' => __('is not empty', 'db-app-builder'),
            'greater_than' => __('is greater than', 'db-app-builder'),
            'less_than' => __('is less than', 'db-app-builder'),
            'greater_equal' => __('is greater than or equal to', 'db-app-builder'),
            'less_equal' => __('is less than or equal to', 'db-app-builder'),
            'is_checked' => __('is checked', 'db-app-builder'),
            'is_not_checked' => __('is not checked', 'db-app-builder'),
            'in_list' => __('is in list', 'db-app-builder'),
            'not_in_list' => __('is not in list', 'db-app-builder')
        );
    }

    /**
     * Get available actions
     */
    public static function get_available_actions() {
        return array(
            'show' => __('Show field/section', 'db-app-builder'),
            'hide' => __('Hide field/section', 'db-app-builder'),
            'enable' => __('Enable field', 'db-app-builder'),
            'disable' => __('Disable field', 'db-app-builder'),
            'set_value' => __('Set field value', 'db-app-builder'),
            'set_required' => __('Make field required', 'db-app-builder'),
            'remove_required' => __('Remove required validation', 'db-app-builder'),
            'show_step' => __('Show form step', 'db-app-builder'),
            'hide_step' => __('Hide form step', 'db-app-builder'),
            'skip_step' => __('Skip to step', 'db-app-builder')
        );
    }

    /**
     * Evaluate conditional logic for a form
     */
    public static function evaluate_form_conditions($form_id, $form_data = array()) {
        $rules = self::get_form_conditional_rules($form_id);
        $results = array();

        foreach ($rules as $rule) {
            $rule_result = self::evaluate_rule($rule, $form_data);
            $results[$rule['id']] = $rule_result;

            if ($rule_result['matched']) {
                // Apply actions
                foreach ($rule['actions'] as $action) {
                    $results[$rule['id']]['actions'][] = array(
                        'type' => $action['type'],
                        'target' => $action['target'],
                        'value' => $action['value']
                    );
                }
            }
        }

        return $results;
    }

    /**
     * Evaluate a single conditional rule
     */
    public static function evaluate_rule($rule, $form_data) {
        $conditions = $rule['conditions'];
        $logic_operator = isset($rule['logic_operator']) ? $rule['logic_operator'] : 'and';

        $condition_results = array();

        foreach ($conditions as $condition) {
            $field_value = isset($form_data[$condition['field']]) ? $form_data[$condition['field']] : '';
            $condition_result = self::evaluate_condition($condition, $field_value);
            $condition_results[] = $condition_result;
        }

        // Apply logic operator
        if ($logic_operator === 'or') {
            $matched = in_array(true, $condition_results);
        } else {
            $matched = !in_array(false, $condition_results);
        }

        return array(
            'matched' => $matched,
            'conditions' => $condition_results,
            'actions' => array()
        );
    }

    /**
     * Evaluate a single condition
     */
    public static function evaluate_condition($condition, $field_value) {
        $operator = $condition['operator'];
        $expected_value = $condition['value'];

        // Ensure we have string values for string operations
        $field_value = is_null($field_value) ? '' : (string)$field_value;
        $expected_value = is_null($expected_value) ? '' : (string)$expected_value;

        switch ($operator) {
            case 'equals':
                return $field_value == $expected_value;

            case 'not_equals':
                return $field_value != $expected_value;

            case 'contains':
                return strpos($field_value, $expected_value) !== false;

            case 'not_contains':
                return strpos($field_value, $expected_value) === false;

            case 'starts_with':
                return strpos($field_value, $expected_value) === 0;

            case 'ends_with':
                return $expected_value !== '' && substr($field_value, -strlen($expected_value)) === $expected_value;

            case 'is_empty':
                return empty($field_value);

            case 'is_not_empty':
                return !empty($field_value);

            case 'greater_than':
                return is_numeric($field_value) && is_numeric($expected_value) && floatval($field_value) > floatval($expected_value);

            case 'less_than':
                return is_numeric($field_value) && is_numeric($expected_value) && floatval($field_value) < floatval($expected_value);

            case 'greater_equal':
                return is_numeric($field_value) && is_numeric($expected_value) && floatval($field_value) >= floatval($expected_value);

            case 'less_equal':
                return is_numeric($field_value) && is_numeric($expected_value) && floatval($field_value) <= floatval($expected_value);

            case 'is_checked':
                return !empty($field_value) && ($field_value === '1' || $field_value === 'on' || $field_value === true);

            case 'is_not_checked':
                return empty($field_value) || ($field_value !== '1' && $field_value !== 'on' && $field_value !== true);

            case 'in_list':
                $list = explode(',', $expected_value);
                $list = array_map('trim', $list);
                return in_array($field_value, $list);

            case 'not_in_list':
                $list = explode(',', $expected_value);
                $list = array_map('trim', $list);
                return !in_array($field_value, $list);

            default:
                return false;
        }
    }

    /**
     * Get conditional rules for a form
     */
    public static function get_form_conditional_rules($form_id) {
        global $wpdb;

        // Check if it's a multi-step form
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        $multistep_form = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $multistep_forms_table WHERE form_id = %d AND is_active = 1",
            $form_id
        ));

        if ($multistep_form && !empty($multistep_form->conditional_rules)) {
            return json_decode($multistep_form->conditional_rules, true);
        }

        // Check for standalone conditional rules
        $conditional_rules_table = $wpdb->prefix . 'dab_conditional_rules';
        $rules = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $conditional_rules_table WHERE multistep_form_id = %d AND is_active = 1",
            $multistep_form ? $multistep_form->id : 0
        ));

        $formatted_rules = array();
        foreach ($rules as $rule) {
            $formatted_rules[] = array(
                'id' => $rule->id,
                'name' => $rule->rule_name,
                'conditions' => array(
                    array(
                        'field' => $rule->trigger_field,
                        'operator' => $rule->trigger_condition,
                        'value' => $rule->trigger_value
                    )
                ),
                'logic_operator' => 'and',
                'actions' => array(
                    array(
                        'type' => $rule->action_type,
                        'target' => $rule->action_target,
                        'value' => $rule->action_value
                    )
                )
            );
        }

        return $formatted_rules;
    }

    /**
     * Render conditional logic builder interface
     */
    public static function render_conditional_builder($form_id, $fields = array()) {
        $rules = self::get_form_conditional_rules($form_id);
        $operators = self::get_available_operators();
        $actions = self::get_available_actions();

        ?>
        <div class="dab-conditional-builder" data-form-id="<?php echo $form_id; ?>">
            <div class="dab-conditional-header">
                <h3><?php _e('Conditional Logic Rules', 'db-app-builder'); ?></h3>
                <button type="button" class="button button-primary dab-add-rule">
                    <?php _e('Add Rule', 'db-app-builder'); ?>
                </button>
            </div>

            <div class="dab-conditional-rules" id="dab-conditional-rules">
                <?php if (empty($rules)): ?>
                    <div class="dab-no-rules">
                        <p><?php _e('No conditional rules defined. Click "Add Rule" to create your first rule.', 'db-app-builder'); ?></p>
                    </div>
                <?php else: ?>
                    <?php foreach ($rules as $rule): ?>
                        <?php self::render_rule_builder($rule, $fields, $operators, $actions); ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <div class="dab-conditional-actions">
                <button type="button" class="button button-primary dab-save-rules">
                    <?php _e('Save Rules', 'db-app-builder'); ?>
                </button>
                <button type="button" class="button dab-test-rules">
                    <?php _e('Test Rules', 'db-app-builder'); ?>
                </button>
            </div>
        </div>

        <!-- Rule Template -->
        <script type="text/template" id="dab-rule-template">
            <?php self::render_rule_template($fields, $operators, $actions); ?>
        </script>

        <!-- Condition Template -->
        <script type="text/template" id="dab-condition-template">
            <?php self::render_condition_template($fields, $operators); ?>
        </script>

        <!-- Action Template -->
        <script type="text/template" id="dab-action-template">
            <?php self::render_action_template($fields, $actions); ?>
        </script>
        <?php
    }

    /**
     * Render a single rule builder
     */
    private static function render_rule_builder($rule, $fields, $operators, $actions) {
        ?>
        <div class="dab-rule" data-rule-id="<?php echo isset($rule['id']) ? $rule['id'] : ''; ?>">
            <div class="dab-rule-header">
                <input type="text" class="dab-rule-name" value="<?php echo esc_attr(isset($rule['name']) ? $rule['name'] : ''); ?>" placeholder="<?php _e('Rule name', 'db-app-builder'); ?>">
                <button type="button" class="button-link-delete dab-delete-rule"><?php _e('Delete', 'db-app-builder'); ?></button>
            </div>

            <div class="dab-rule-body">
                <div class="dab-conditions-section">
                    <h4><?php _e('When', 'db-app-builder'); ?></h4>
                    <div class="dab-conditions">
                        <?php if (!empty($rule['conditions'])): ?>
                            <?php foreach ($rule['conditions'] as $condition): ?>
                                <?php self::render_condition($condition, $fields, $operators); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <button type="button" class="button dab-add-condition"><?php _e('Add Condition', 'db-app-builder'); ?></button>

                    <div class="dab-logic-operator">
                        <label>
                            <input type="radio" name="logic_operator_<?php echo isset($rule['id']) ? $rule['id'] : ''; ?>" value="and" <?php checked(isset($rule['logic_operator']) ? $rule['logic_operator'] : 'and', 'and'); ?>>
                            <?php _e('AND (all conditions must be true)', 'db-app-builder'); ?>
                        </label>
                        <label>
                            <input type="radio" name="logic_operator_<?php echo isset($rule['id']) ? $rule['id'] : ''; ?>" value="or" <?php checked(isset($rule['logic_operator']) ? $rule['logic_operator'] : 'and', 'or'); ?>>
                            <?php _e('OR (any condition can be true)', 'db-app-builder'); ?>
                        </label>
                    </div>
                </div>

                <div class="dab-actions-section">
                    <h4><?php _e('Then', 'db-app-builder'); ?></h4>
                    <div class="dab-actions">
                        <?php if (!empty($rule['actions'])): ?>
                            <?php foreach ($rule['actions'] as $action): ?>
                                <?php self::render_action($action, $fields, $actions); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <button type="button" class="button dab-add-action"><?php _e('Add Action', 'db-app-builder'); ?></button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render a condition
     */
    private static function render_condition($condition, $fields, $operators) {
        ?>
        <div class="dab-condition">
            <select class="dab-condition-field">
                <option value=""><?php _e('Select field', 'db-app-builder'); ?></option>
                <?php foreach ($fields as $field): ?>
                    <option value="<?php echo esc_attr($field->field_slug); ?>" <?php selected(isset($condition['field']) ? $condition['field'] : '', $field->field_slug); ?>>
                        <?php echo esc_html($field->field_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>

            <select class="dab-condition-operator">
                <?php foreach ($operators as $op_value => $op_label): ?>
                    <option value="<?php echo esc_attr($op_value); ?>" <?php selected(isset($condition['operator']) ? $condition['operator'] : '', $op_value); ?>>
                        <?php echo esc_html($op_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>

            <input type="text" class="dab-condition-value" value="<?php echo esc_attr(isset($condition['value']) ? $condition['value'] : ''); ?>" placeholder="<?php _e('Value', 'db-app-builder'); ?>">

            <button type="button" class="button-link-delete dab-delete-condition"><?php _e('Delete', 'db-app-builder'); ?></button>
        </div>
        <?php
    }

    /**
     * Render an action
     */
    private static function render_action($action, $fields, $actions) {
        ?>
        <div class="dab-action">
            <select class="dab-action-type">
                <?php foreach ($actions as $action_value => $action_label): ?>
                    <option value="<?php echo esc_attr($action_value); ?>" <?php selected(isset($action['type']) ? $action['type'] : '', $action_value); ?>>
                        <?php echo esc_html($action_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>

            <select class="dab-action-target">
                <option value=""><?php _e('Select target', 'db-app-builder'); ?></option>
                <?php foreach ($fields as $field): ?>
                    <option value="<?php echo esc_attr($field->field_slug); ?>" <?php selected(isset($action['target']) ? $action['target'] : '', $field->field_slug); ?>>
                        <?php echo esc_html($field->field_label); ?>
                    </option>
                <?php endforeach; ?>
            </select>

            <input type="text" class="dab-action-value" value="<?php echo esc_attr(isset($action['value']) ? $action['value'] : ''); ?>" placeholder="<?php _e('Value (if needed)', 'db-app-builder'); ?>">

            <button type="button" class="button-link-delete dab-delete-action"><?php _e('Delete', 'db-app-builder'); ?></button>
        </div>
        <?php
    }

    /**
     * Render rule template for JavaScript
     */
    private static function render_rule_template($fields, $operators, $actions) {
        // This would contain the HTML template for creating new rules
        // Implementation would be similar to render_rule_builder but with placeholders
    }

    /**
     * Render condition template for JavaScript
     */
    private static function render_condition_template($fields, $operators) {
        // This would contain the HTML template for creating new conditions
        // Implementation would be similar to render_condition but with placeholders
    }

    /**
     * Render action template for JavaScript
     */
    private static function render_action_template($fields, $actions) {
        // This would contain the HTML template for creating new actions
        // Implementation would be similar to render_action but with placeholders
    }

    /**
     * AJAX handler to evaluate conditions
     */
    public static function evaluate_conditions() {
        check_ajax_referer('dab_conditional_nonce', 'nonce');

        $form_id = intval($_POST['form_id']);
        $form_data = $_POST['form_data'];

        if (!$form_id) {
            wp_send_json_error(__('Invalid form ID', 'db-app-builder'));
        }

        $results = self::evaluate_form_conditions($form_id, $form_data);

        wp_send_json_success($results);
    }

    /**
     * AJAX handler to save conditional rules
     */
    public static function save_conditional_rules() {
        check_ajax_referer('dab_conditional_nonce', 'nonce');

        $form_id = intval($_POST['form_id']);
        $rules = $_POST['rules'];

        if (!$form_id) {
            wp_send_json_error(__('Invalid form ID', 'db-app-builder'));
        }

        // Save rules to multi-step form if it exists
        global $wpdb;
        $multistep_forms_table = $wpdb->prefix . 'dab_multistep_forms';
        $multistep_form = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $multistep_forms_table WHERE form_id = %d AND is_active = 1",
            $form_id
        ));

        if ($multistep_form) {
            $result = $wpdb->update($multistep_forms_table, array(
                'conditional_rules' => json_encode($rules)
            ), array('id' => $multistep_form->id));

            if ($result !== false) {
                wp_send_json_success(__('Conditional rules saved successfully', 'db-app-builder'));
            } else {
                wp_send_json_error(__('Failed to save conditional rules', 'db-app-builder'));
            }
        } else {
            wp_send_json_error(__('Multi-step form not found', 'db-app-builder'));
        }
    }

    /**
     * AJAX handler to get conditional rules
     */
    public static function get_conditional_rules() {
        check_ajax_referer('dab_conditional_nonce', 'nonce');

        $form_id = intval($_POST['form_id']);

        if (!$form_id) {
            wp_send_json_error(__('Invalid form ID', 'db-app-builder'));
        }

        $rules = self::get_form_conditional_rules($form_id);

        wp_send_json_success($rules);
    }
}
