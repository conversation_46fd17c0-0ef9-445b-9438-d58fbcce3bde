<?php
/**
 * App Templates Admin Page
 *
 * Displays and manages business application templates
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Handle template installation
if (isset($_POST['install_template']) && wp_verify_nonce($_POST['_wpnonce'], 'dab_install_template')) {
    $template_id = intval($_POST['template_id']);
    $installation_name = sanitize_text_field($_POST['installation_name']);

    if ($template_id && $installation_name) {
        // This would normally be handled via AJAX, but we can also handle it here
        echo '<div class="notice notice-info"><p>' . __('Template installation initiated. Please use the Install button for real-time progress.', 'db-app-builder') . '</p></div>';
    }
}
?>

<div class="wrap dab-admin-wrap">
    <div class="dab-admin-header">
        <h1 class="dab-admin-title">
            <span class="dashicons dashicons-admin-appearance"></span>
            <?php _e('Business Application Templates', 'db-app-builder'); ?>
        </h1>
        <p class="dab-admin-description">
            <?php _e('Choose from pre-built business application templates to quickly set up common workflows and data structures.', 'db-app-builder'); ?>
        </p>
    </div>

    <div class="dab-templates-container">
        <!-- Template Filters -->
        <div class="dab-templates-filters">
            <div class="dab-filter-group">
                <label for="template-search"><?php _e('Search Templates:', 'db-app-builder'); ?></label>
                <input type="text" id="template-search" class="dab-search-input" placeholder="<?php _e('Search by name or description...', 'db-app-builder'); ?>">
            </div>

            <div class="dab-filter-group">
                <label for="template-category"><?php _e('Category:', 'db-app-builder'); ?></label>
                <select id="template-category" class="dab-filter-select">
                    <option value=""><?php _e('All Categories', 'db-app-builder'); ?></option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo esc_attr($category->category); ?>">
                            <?php echo esc_html(ucwords(str_replace('_', ' ', $category->category))); ?>
                            (<?php echo intval($category->template_count); ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <button type="button" id="search-templates" class="dab-btn dab-btn-primary">
                <span class="dashicons dashicons-search"></span>
                <?php _e('Search', 'db-app-builder'); ?>
            </button>
        </div>

        <!-- Template Grid -->
        <div class="dab-templates-grid" id="templates-grid">
            <?php foreach ($templates as $template): ?>
                <div class="dab-template-card" data-category="<?php echo esc_attr($template->category); ?>">
                    <div class="dab-template-header">
                        <div class="dab-template-icon">
                            <span class="dashicons <?php echo esc_attr($template->icon ?: 'dashicons-admin-generic'); ?>"></span>
                        </div>
                        <div class="dab-template-meta">
                            <h3 class="dab-template-title"><?php echo esc_html($template->name); ?></h3>
                            <span class="dab-template-category">
                                <?php echo esc_html(ucwords(str_replace('_', ' ', $template->category))); ?>
                            </span>
                        </div>
                    </div>

                    <div class="dab-template-body">
                        <p class="dab-template-description"><?php echo esc_html($template->description); ?></p>

                        <div class="dab-template-stats">
                            <span class="dab-install-count">
                                <span class="dashicons dashicons-download"></span>
                                <?php printf(__('%d installs', 'db-app-builder'), intval($template->install_count)); ?>
                            </span>
                            <?php if ($template->rating > 0): ?>
                                <span class="dab-rating">
                                    <span class="dashicons dashicons-star-filled"></span>
                                    <?php echo number_format($template->rating, 1); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="dab-template-footer">
                        <button type="button" class="dab-btn dab-btn-outline-primary dab-preview-template"
                                data-template-id="<?php echo intval($template->id); ?>">
                            <span class="dashicons dashicons-visibility"></span>
                            <?php _e('Preview', 'db-app-builder'); ?>
                        </button>

                        <button type="button" class="dab-btn dab-btn-primary dab-install-template"
                                data-template-id="<?php echo intval($template->id); ?>"
                                data-template-name="<?php echo esc_attr($template->name); ?>">
                            <span class="dashicons dashicons-download"></span>
                            <?php _e('Install', 'db-app-builder'); ?>
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if (empty($templates)): ?>
            <div class="dab-no-templates">
                <div class="dab-no-templates-icon">
                    <span class="dashicons dashicons-admin-appearance"></span>
                </div>
                <h3><?php _e('No Templates Available', 'db-app-builder'); ?></h3>
                <p><?php _e('No business application templates are currently available.', 'db-app-builder'); ?></p>

                <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
                    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-left: 4px solid #0073aa;">
                        <h4>Debug Information</h4>
                        <p><strong>Templates table exists:</strong> <?php echo $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}dab_app_templates'") ? 'Yes' : 'No'; ?></p>
                        <p><strong>Total templates in DB:</strong> <?php echo $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}dab_app_templates"); ?></p>
                        <p><strong>Active templates:</strong> <?php echo $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}dab_app_templates WHERE is_active = 1"); ?></p>
                        <p><strong>System templates:</strong> <?php echo $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}dab_app_templates WHERE is_system = 1"); ?></p>
                    </div>
                <?php endif; ?>

                <p>
                    <a href="<?php echo admin_url('admin.php?page=dab_template_setup'); ?>" class="button button-primary">
                        <?php _e('Setup Templates', 'db-app-builder'); ?>
                    </a>
                </p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Installed Templates Section -->
    <?php if (!empty($installed_templates)): ?>
        <div class="dab-installed-templates">
            <h2 class="dab-section-title">
                <span class="dashicons dashicons-yes-alt"></span>
                <?php _e('Installed Templates', 'db-app-builder'); ?>
            </h2>

            <div class="dab-installed-grid">
                <?php foreach ($installed_templates as $installation): ?>
                    <div class="dab-installed-card">
                        <div class="dab-installed-header">
                            <h4><?php echo esc_html($installation->installation_name); ?></h4>
                            <span class="dab-installed-date">
                                <?php printf(__('Installed: %s', 'db-app-builder'),
                                    date_i18n(get_option('date_format'), strtotime($installation->installed_at))); ?>
                            </span>
                        </div>

                        <div class="dab-installed-body">
                            <p><strong><?php _e('Template:', 'db-app-builder'); ?></strong> <?php echo esc_html($installation->template_key); ?></p>

                            <?php
                            $installation_data = json_decode($installation->installation_data, true);
                            if ($installation_data): ?>
                                <div class="dab-installation-summary">
                                    <?php if (isset($installation_data['tables'])): ?>
                                        <span class="dab-summary-item">
                                            <span class="dashicons dashicons-database"></span>
                                            <?php printf(__('%d Tables', 'db-app-builder'), count($installation_data['tables'])); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if (isset($installation_data['forms'])): ?>
                                        <span class="dab-summary-item">
                                            <span class="dashicons dashicons-feedback"></span>
                                            <?php printf(__('%d Forms', 'db-app-builder'), count($installation_data['forms'])); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if (isset($installation_data['views'])): ?>
                                        <span class="dab-summary-item">
                                            <span class="dashicons dashicons-visibility"></span>
                                            <?php printf(__('%d Views', 'db-app-builder'), count($installation_data['views'])); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="dab-installed-footer">
                            <a href="<?php echo admin_url('admin.php?page=dab_tables'); ?>" class="dab-btn dab-btn-outline-primary">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Manage', 'db-app-builder'); ?>
                            </a>

                            <button type="button" class="dab-btn dab-btn-outline-danger dab-delete-installation"
                                    data-installation-id="<?php echo intval($installation->id); ?>"
                                    data-installation-name="<?php echo esc_attr($installation->installation_name); ?>">
                                <span class="dashicons dashicons-trash"></span>
                                <?php _e('Delete', 'db-app-builder'); ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Template Preview Modal -->
<div id="dab-template-preview-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3 id="dab-preview-title"><?php _e('Template Preview', 'db-app-builder'); ?></h3>
            <button type="button" class="dab-modal-close">&times;</button>
        </div>
        <div class="dab-modal-body" id="dab-preview-content">
            <!-- Preview content will be loaded here -->
        </div>
        <div class="dab-modal-footer">
            <button type="button" class="dab-btn dab-btn-outline-secondary dab-modal-close">
                <?php _e('Close', 'db-app-builder'); ?>
            </button>
            <button type="button" class="dab-btn dab-btn-primary" id="dab-install-from-preview">
                <span class="dashicons dashicons-download"></span>
                <?php _e('Install Template', 'db-app-builder'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Template Installation Modal -->
<div id="dab-template-install-modal" class="dab-modal" style="display: none;">
    <div class="dab-modal-content">
        <div class="dab-modal-header">
            <h3><?php _e('Install Template', 'db-app-builder'); ?></h3>
            <button type="button" class="dab-modal-close">&times;</button>
        </div>
        <div class="dab-modal-body">
            <form id="dab-install-template-form">
                <div class="dab-form-group">
                    <label for="installation-name"><?php _e('Installation Name:', 'db-app-builder'); ?></label>
                    <input type="text" id="installation-name" name="installation_name" class="dab-form-control"
                           placeholder="<?php _e('Enter a unique name for this installation', 'db-app-builder'); ?>" required>
                    <p class="dab-form-help">
                        <?php _e('This name will be used as a prefix for all created tables and forms to avoid conflicts.', 'db-app-builder'); ?>
                    </p>
                </div>

                <div class="dab-installation-progress" id="installation-progress" style="display: none;">
                    <div class="dab-progress-bar">
                        <div class="dab-progress-fill"></div>
                    </div>
                    <p class="dab-progress-text"><?php _e('Installing template...', 'db-app-builder'); ?></p>
                </div>
            </form>
        </div>
        <div class="dab-modal-footer">
            <button type="button" class="dab-btn dab-btn-outline-secondary dab-modal-close">
                <?php _e('Cancel', 'db-app-builder'); ?>
            </button>
            <button type="button" class="dab-btn dab-btn-primary" id="dab-confirm-install">
                <span class="dashicons dashicons-download"></span>
                <?php _e('Install Now', 'db-app-builder'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Modal Overlay -->
<div class="dab-modal-overlay" style="display: none;"></div>
