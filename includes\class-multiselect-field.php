<?php
/**
 * Multi-select Dropdown Field Handler
 *
 * @package    Database_App_Builder
 * @subpackage Database_App_Builder/includes
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class DAB_Multiselect_Field {
    /**
     * Initialize the class
     */
    public function __construct() {
        // Register the multi-select field type
        add_filter('dab_field_types', array($this, 'register_multiselect_field_type'));

        // Add field options
        add_filter('dab_field_type_options', array($this, 'add_multiselect_field_options'), 10, 2);

        // Register field renderer
        add_action('dab_render_field_multiselect', array($this, 'render_multiselect_field'), 10, 2);

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Format multiselect value for display
        add_filter('dab_format_field_value', array($this, 'format_multiselect_value'), 10, 3);
    }

    /**
     * Register the multi-select field type
     *
     * @param array $field_types Existing field types
     * @return array Modified field types
     */
    public function register_multiselect_field_type($field_types) {
        $field_types['multiselect'] = __('Multi-select Dropdown', 'db-app-builder');
        return $field_types;
    }

    /**
     * Add multi-select field options
     *
     * @param array $options Existing options
     * @param string $field_type Field type
     * @return array Modified options
     */
    public function add_multiselect_field_options($options, $field_type) {
        if ($field_type === 'multiselect') {
            $options = array(
                'options' => array(
                    'label' => __('Options', 'db-app-builder'),
                    'type' => 'textarea',
                    'description' => __('Enter one option per line. For key-value pairs, use format: key:value', 'db-app-builder'),
                ),
                'placeholder' => array(
                    'label' => __('Placeholder', 'db-app-builder'),
                    'type' => 'text',
                    'description' => __('Text to display when no option is selected.', 'db-app-builder'),
                ),
                'max_selections' => array(
                    'label' => __('Maximum Selections', 'db-app-builder'),
                    'type' => 'number',
                    'min' => 0,
                    'description' => __('Maximum number of options that can be selected (0 for unlimited).', 'db-app-builder'),
                ),
                'allow_search' => array(
                    'label' => __('Allow Search', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Enable search functionality within the dropdown.', 'db-app-builder'),
                ),
                'select_all' => array(
                    'label' => __('Show "Select All" Option', 'db-app-builder'),
                    'type' => 'checkbox',
                    'description' => __('Add a "Select All" option to the dropdown.', 'db-app-builder'),
                ),
                'option_groups' => array(
                    'label' => __('Option Groups', 'db-app-builder'),
                    'type' => 'textarea',
                    'description' => __('Define option groups. Format: group_name:option1,option2,option3 (one group per line)', 'db-app-builder'),
                ),
            );
        }

        return $options;
    }

    /**
     * Render the multi-select field
     *
     * @param object $field Field object
     * @param mixed $value Field value
     */
    public function render_multiselect_field($field, $value) {
        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }

        // Get field settings with defaults
        $placeholder = isset($options['placeholder']) ? $options['placeholder'] : __('Select options', 'db-app-builder');
        $max_selections = isset($options['max_selections']) ? intval($options['max_selections']) : 0;
        $allow_search = isset($options['allow_search']) ? (bool)$options['allow_search'] : true;
        $select_all = isset($options['select_all']) ? (bool)$options['select_all'] : false;

        // Parse options
        $dropdown_options = $this->parse_options($options['options'] ?? '');
        $option_groups = $this->parse_option_groups($options['option_groups'] ?? '');

        // Parse the value (stored as JSON array)
        $selected_values = array();
        if (!empty($value)) {
            $value_string = is_null($value) ? '' : (string)$value;
            if (is_string($value) && $value_string !== '' && strpos($value_string, '[') === 0) {
                $selected_values = json_decode($value, true);
                if (!is_array($selected_values)) {
                    $selected_values = array();
                }
            } else if (is_string($value) && $value_string !== '') {
                // Handle comma-separated values for backward compatibility
                $selected_values = explode(',', $value);
            }
        }

        // Generate a unique ID for the field
        $field_id = 'dab-multiselect-' . $field->id . '-' . uniqid();

        // Output the field HTML
        ?>
        <div class="dab-multiselect-field" id="<?php echo esc_attr($field_id); ?>" data-field-slug="<?php echo esc_attr($field->field_slug); ?>">
            <select name="<?php echo esc_attr($field->field_slug . '[]'); ?>"
                   id="<?php echo esc_attr($field_id . '-select'); ?>"
                   class="dab-multiselect"
                   multiple="multiple"
                   data-placeholder="<?php echo esc_attr($placeholder); ?>"
                   data-allow-search="<?php echo $allow_search ? 'true' : 'false'; ?>"
                   data-max-selections="<?php echo esc_attr($max_selections); ?>"
                   data-select-all="<?php echo $select_all ? 'true' : 'false'; ?>"
                   <?php echo $field->required ? 'required' : ''; ?>>

                <?php if (!empty($option_groups)): ?>
                    <?php foreach ($option_groups as $group_name => $group_options): ?>
                        <optgroup label="<?php echo esc_attr($group_name); ?>">
                            <?php foreach ($group_options as $option_value => $option_label): ?>
                                <option value="<?php echo esc_attr($option_value); ?>"
                                        <?php echo in_array($option_value, $selected_values) ? 'selected' : ''; ?>>
                                    <?php echo esc_html($option_label); ?>
                                </option>
                            <?php endforeach; ?>
                        </optgroup>
                    <?php endforeach; ?>
                <?php else: ?>
                    <?php foreach ($dropdown_options as $option_value => $option_label): ?>
                        <option value="<?php echo esc_attr($option_value); ?>"
                                <?php echo in_array($option_value, $selected_values) ? 'selected' : ''; ?>>
                            <?php echo esc_html($option_label); ?>
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
            </select>

            <!-- Hidden field to store the combined JSON value -->
            <input type="hidden" name="<?php echo esc_attr($field->field_slug); ?>" id="<?php echo esc_attr($field_id . '-value'); ?>" value="<?php echo esc_attr($value); ?>">
        </div>

        <script>
        jQuery(document).ready(function($) {
            var $field = $('#<?php echo esc_js($field_id); ?>');
            var $select = $('#<?php echo esc_js($field_id . '-select'); ?>');
            var $value = $('#<?php echo esc_js($field_id . '-value'); ?>');

            // Initialize Select2
            $select.select2({
                placeholder: $select.data('placeholder'),
                allowClear: true,
                multiple: true,
                width: '100%',
                maximumSelectionLength: $select.data('max-selections') > 0 ? $select.data('max-selections') : null,
                minimumResultsForSearch: $select.data('allow-search') ? 0 : Infinity
            });

            // Add "Select All" option if enabled
            if ($select.data('select-all')) {
                addSelectAllOption();
            }

            // Update the hidden value field when selection changes
            $select.on('change', function() {
                updateValue();
            });

            // Initialize the hidden value
            updateValue();

            // Function to update the hidden value field
            function updateValue() {
                var selectedValues = $select.val() || [];

                // Remove "select-all" from the actual values if it exists
                var valueIndex = selectedValues.indexOf('select-all');
                if (valueIndex > -1) {
                    selectedValues.splice(valueIndex, 1);
                }

                // Update the hidden value field with JSON
                $value.val(JSON.stringify(selectedValues));
            }

            // Function to add "Select All" option
            function addSelectAllOption() {
                // Add the "Select All" option at the top
                $select.prepend('<option value="select-all"><?php echo esc_js(__('Select All', 'db-app-builder')); ?></option>');

                // Handle "Select All" option
                $select.on('change', function(e) {
                    if (e.target.value === 'select-all') {
                        var allOptions = [];
                        $select.find('option').each(function() {
                            var value = $(this).val();
                            if (value !== 'select-all') {
                                allOptions.push(value);
                            }
                        });

                        $select.val(allOptions);
                        $select.trigger('change');
                    }
                });
            }
        });
        </script>
        <?php
    }

    /**
     * Format multiselect value for display
     *
     * @param mixed $value Field value
     * @param object $field Field object
     * @param string $context Display context
     * @return string Formatted value
     */
    public function format_multiselect_value($value, $field, $context) {
        if ($field->field_type !== 'multiselect') {
            return $value;
        }

        // Get field options
        $options = json_decode($field->options, true);
        if (!is_array($options)) {
            $options = array();
        }

        // Parse dropdown options
        $dropdown_options = $this->parse_options($options['options'] ?? '');
        $option_groups = $this->parse_option_groups($options['option_groups'] ?? '');

        // Combine all options from groups
        if (!empty($option_groups)) {
            foreach ($option_groups as $group_options) {
                $dropdown_options = array_merge($dropdown_options, $group_options);
            }
        }

        // Parse the value
        $selected_values = array();
        if (!empty($value)) {
            if (is_string($value) && strpos($value, '[') === 0) {
                $selected_values = json_decode($value, true);
                if (!is_array($selected_values)) {
                    $selected_values = array();
                }
            } else if (is_string($value)) {
                // Handle comma-separated values for backward compatibility
                $selected_values = explode(',', $value);
            }
        }

        if (empty($selected_values)) {
            return '';
        }

        // Format the selected values
        $formatted_values = array();
        foreach ($selected_values as $selected_value) {
            if (isset($dropdown_options[$selected_value])) {
                $formatted_values[] = $dropdown_options[$selected_value];
            } else {
                $formatted_values[] = $selected_value;
            }
        }

        // Join the formatted values
        return implode(', ', $formatted_values);
    }

    /**
     * Parse options from a string
     *
     * @param string $options_string Options string
     * @return array Parsed options
     */
    private function parse_options($options_string) {
        $options = array();

        if (empty($options_string)) {
            return $options;
        }

        $lines = explode("\n", $options_string);
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            // Check if the line has a key:value format
            if (strpos($line, ':') !== false) {
                list($key, $value) = explode(':', $line, 2);
                $key = trim($key);
                $value = trim($value);
                $options[$key] = $value;
            } else {
                $options[$line] = $line;
            }
        }

        return $options;
    }

    /**
     * Parse option groups from a string
     *
     * @param string $groups_string Option groups string
     * @return array Parsed option groups
     */
    private function parse_option_groups($groups_string) {
        $groups = array();

        if (empty($groups_string)) {
            return $groups;
        }

        $lines = explode("\n", $groups_string);
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || strpos($line, ':') === false) {
                continue;
            }

            list($group_name, $options_string) = explode(':', $line, 2);
            $group_name = trim($group_name);
            $options_string = trim($options_string);

            if (empty($group_name) || empty($options_string)) {
                continue;
            }

            $options = explode(',', $options_string);
            $group_options = array();

            foreach ($options as $option) {
                $option = trim($option);
                if (!empty($option)) {
                    $group_options[$option] = $option;
                }
            }

            if (!empty($group_options)) {
                $groups[$group_name] = $group_options;
            }
        }

        return $groups;
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue Select2 library
        wp_enqueue_style(
            'select2',
            'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
            array(),
            '4.1.0-rc.0'
        );

        wp_enqueue_script(
            'select2',
            'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js',
            array('jquery'),
            '4.1.0-rc.0',
            true
        );

        // Enqueue custom styles
        wp_enqueue_style(
            'dab-multiselect-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/multiselect-field.css',
            array('select2'),
            DAB_VERSION
        );

        // Enqueue custom scripts
        wp_enqueue_script(
            'dab-multiselect-field',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/multiselect-field.js',
            array('jquery', 'select2'),
            DAB_VERSION,
            true
        );
    }
}

// Initialize the class
new DAB_Multiselect_Field();
